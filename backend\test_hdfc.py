#!/usr/bin/env python3
"""
Test HDFC Bank processor functionality
"""

import pandas as pd
from hdfc_processor import HDFCBankProcessor
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG)

def test_hdfc_detection():
    """Test HDFC Bank detection"""
    processor = HDFCBankProcessor()
    
    # Test cases
    test_cases = [
        {
            'text': 'HDFC BANK LIMITED Statement of Account Customer ID: 12345',
            'expected': True,
            'description': 'Standard HDFC statement'
        },
        {
            'text': 'HDFC Bank Account Number: ********** IFSC Code: HDFC0001234',
            'expected': True,
            'description': 'HDFC with account details'
        },
        {
            'text': 'State Bank of India Statement',
            'expected': False,
            'description': 'SBI statement (should fail)'
        },
        {
            'text': 'ICICI Bank Limited Statement',
            'expected': False,
            'description': 'ICICI statement (should fail)'
        },
        {
            'text': 'HDFC BANK www.hdfcbank.com Branch: Mumbai',
            'expected': True,
            'description': 'HDFC with website and branch'
        }
    ]
    
    print("🔍 Testing HDFC Bank Detection:")
    for i, case in enumerate(test_cases, 1):
        is_hdfc, confidence = processor.detect_hdfc_bank(case['text'])
        status = "✅" if is_hdfc == case['expected'] else "❌"
        print(f"{status} Test {i}: {case['description']}")
        print(f"   Text: {case['text'][:50]}...")
        print(f"   Expected: {case['expected']}, Got: {is_hdfc}, Confidence: {confidence:.2f}")
        print()

def test_hdfc_metadata_extraction():
    """Test HDFC metadata extraction"""
    processor = HDFCBankProcessor()
    
    sample_text = """
    HDFC BANK LIMITED
    Statement of Account
    Account Number: **********123456
    Customer ID: ********
    IFSC Code: HDFC0001234
    Branch: MUMBAI MAIN BRANCH
    Statement Period: 01-Jan-2024 to 31-Jan-2024
    """
    
    print("📋 Testing HDFC Metadata Extraction:")
    metadata = processor.extract_hdfc_metadata(sample_text)
    
    for key, value in metadata.items():
        print(f"   {key}: {value}")

def test_hdfc_dataframe_processing():
    """Test HDFC DataFrame processing"""
    processor = HDFCBankProcessor()
    
    # Create sample HDFC statement data
    sample_data = {
        0: ['Date', '01/01/2024', '02/01/2024', '03/01/2024', '04/01/2024'],
        1: ['Narration', 'ATM WITHDRAWAL', 'SALARY CREDIT', 'ONLINE TRANSFER', 'UPI PAYMENT'],
        2: ['Chq./Ref.No.', 'REF001', 'REF002', 'REF003', 'REF004'],
        3: ['Value Dt', '01/01/2024', '02/01/2024', '03/01/2024', '04/01/2024'],
        4: ['Withdrawal Amt.', '5000.00', '', '2000.00', '500.00'],
        5: ['Deposit Amt.', '', '50000.00', '', ''],
        6: ['Closing Balance', '45000.00', '95000.00', '93000.00', '92500.00']
    }
    
    df = pd.DataFrame(sample_data)
    print("📊 Testing HDFC DataFrame Processing:")
    print("Sample DataFrame:")
    print(df)
    print()
    
    transactions = processor.process_hdfc_dataframe(df)
    
    print(f"✅ Extracted {len(transactions)} HDFC transactions:")
    for i, txn in enumerate(transactions, 1):
        print(f"{i}. Date: {txn['date']}")
        print(f"   Narration: {txn['narration']}")
        print(f"   Chq/Ref: {txn['chq_ref_no']}")
        print(f"   Withdrawal: {txn['withdrawal_amt']}")
        print(f"   Deposit: {txn['deposit_amt']}")
        print(f"   Balance: {txn['closing_balance']}")
        print()

def test_hdfc_date_parsing():
    """Test HDFC date parsing"""
    processor = HDFCBankProcessor()
    
    test_dates = [
        '01/01/2024',
        '1-Jan-2024',
        '01-01-24',
        '2024-01-01',
        '01 Jan 2024',
        '1/1/24',
        'invalid_date',
        '32/13/2024',  # Invalid date
        ''
    ]
    
    print("📅 Testing HDFC Date Parsing:")
    for date_str in test_dates:
        parsed = processor._parse_hdfc_date(date_str)
        print(f"'{date_str}' -> {parsed}")

def test_hdfc_amount_parsing():
    """Test HDFC amount parsing"""
    processor = HDFCBankProcessor()
    
    test_amounts = [
        '5000.00',
        '5,000.00',
        '50,000.50',
        'Rs. 1000',
        '₹ 2000',
        '(500.00)',  # Negative
        '1,50,000.00',  # Indian format
        'invalid_amount',
        '',
        '-'
    ]
    
    print("💰 Testing HDFC Amount Parsing:")
    for amount_str in test_amounts:
        parsed = processor._parse_hdfc_amount(amount_str)
        print(f"'{amount_str}' -> {parsed}")

if __name__ == "__main__":
    print("🏦 Testing HDFC Bank Processor\n")
    
    test_hdfc_detection()
    print("="*60)
    test_hdfc_metadata_extraction()
    print("="*60)
    test_hdfc_date_parsing()
    print("="*60)
    test_hdfc_amount_parsing()
    print("="*60)
    test_hdfc_dataframe_processing()
    
    print("\n🎉 All HDFC tests completed!")
