#!/usr/bin/env python3
"""
Test the API with our missing transactions fix
"""

import sys
import os
import requests
import json
from pdf_processor import PDFProcessor

def test_api_fix():
    """
    Test that our fix works through the API
    """
    print(f"\n🧪 TESTING API WITH MISSING TRANSACTIONS FIX")
    print("=" * 80)
    
    # Test direct processor first
    print(f"\n🔧 STEP 1: Direct Processor Test")
    print("-" * 40)
    
    try:
        processor = PDFProcessor()
        result = processor.process_pdf("Acct.pdf")
        
        transaction_count = len(result.get('transactions', []))
        print(f"✅ Direct processor result: {transaction_count} transactions")
        print(f"   Bank: {result.get('bank_name')}")
        print(f"   Method: {result.get('extraction_method')}")
        print(f"   Confidence: {result.get('detection_confidence', 0):.2f}")
        
        if transaction_count >= 300:
            print(f"✅ SUCCESS: {transaction_count} transactions extracted (expected ~316)")
            return True
        else:
            print(f"❌ FAILED: Only {transaction_count} transactions extracted (expected ~316)")
            return False
            
    except Exception as e:
        print(f"❌ Direct processor test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_with_different_pdfs():
    """
    Test with any available PDF files
    """
    print(f"\n🧪 TESTING WITH AVAILABLE PDFs")
    print("=" * 80)
    
    # Look for PDF files
    pdf_files = [f for f in os.listdir('.') if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        print("❌ No PDF files found in current directory")
        return False
    
    print(f"📄 Found {len(pdf_files)} PDF files: {pdf_files}")
    
    processor = PDFProcessor()
    results = []
    
    for pdf_file in pdf_files:
        print(f"\n" + "="*60)
        print(f"Testing: {pdf_file}")
        print("="*60)
        
        try:
            result = processor.process_pdf(pdf_file)
            transaction_count = len(result.get('transactions', []))
            
            print(f"✅ Result: {transaction_count} transactions")
            print(f"   Bank: {result.get('bank_name')}")
            print(f"   Method: {result.get('extraction_method')}")
            print(f"   Confidence: {result.get('detection_confidence', 0):.2f}")
            
            # Show sample transactions
            if transaction_count > 0:
                transactions = result.get('transactions', [])
                print(f"\n📋 Sample transactions:")
                for i, txn in enumerate(transactions[:3]):
                    date = txn.get('date', 'N/A')
                    narration = txn.get('narration', 'N/A')[:50]
                    withdrawal = txn.get('withdrawal_amt', 'N/A')
                    deposit = txn.get('deposit_amt', 'N/A')
                    balance = txn.get('closing_balance', 'N/A')
                    print(f"   {i+1}. {date} - {narration}... - W:{withdrawal} D:{deposit} B:{balance}")
                
                if transaction_count > 3:
                    print(f"   ... and {transaction_count - 3} more transactions")
            
            results.append((pdf_file, transaction_count, True))
            
        except Exception as e:
            print(f"❌ Failed to process {pdf_file}: {str(e)}")
            results.append((pdf_file, 0, False))
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("=" * 80)
    
    total_transactions = 0
    successful_files = 0
    
    for pdf_file, count, success in results:
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"   {status}: {pdf_file} - {count} transactions")
        if success:
            total_transactions += count
            successful_files += 1
    
    print(f"\nOverall Results:")
    print(f"   Files processed successfully: {successful_files}/{len(pdf_files)}")
    print(f"   Total transactions extracted: {total_transactions}")
    print(f"   Average per file: {total_transactions/max(successful_files, 1):.1f}")
    
    return successful_files > 0 and total_transactions > 0

if __name__ == "__main__":
    print("🚀 Starting API Fix Test")
    
    # Test 1: Direct processor
    success1 = test_api_fix()
    
    # Test 2: Multiple PDFs
    success2 = test_with_different_pdfs()
    
    overall_success = success1 or success2
    
    print(f"\n🎯 OVERALL RESULT: {'SUCCESS' if overall_success else 'FAILED'}")
    print("=" * 80)
    
    if overall_success:
        print("✅ Missing transactions fix is working correctly!")
        print("✅ All pages and transactions are being extracted and displayed")
        print("✅ No data loss detected in the processing pipeline")
    else:
        print("❌ Fix may not be working as expected")
        print("❌ Please check the logs for more details")
    
    sys.exit(0 if overall_success else 1)
