#!/usr/bin/env python3
"""
Test script to verify HDFC Bank column separation fix
Ensures data is properly separated into columns instead of merged into one
"""

import sys
import os
import logging
import pandas as pd
import tabula
from pdf_processor import PDFProcessor
from hdfc_processor import HDFCBankProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_column_separation(pdf_path: str):
    """
    Test that HDFC data is properly separated into columns
    """
    if not os.path.exists(pdf_path):
        logger.error(f"PDF file not found: {pdf_path}")
        return False
    
    try:
        logger.info(f"Testing column separation for: {pdf_path}")
        
        # Test different extraction strategies
        test_extraction_strategies(pdf_path)
        
        # Test full processing
        processor = PDFProcessor()
        result = processor.process_pdf(pdf_path)
        
        if not result.get('success', True):
            logger.error(f"Processing failed: {result.get('message', 'Unknown error')}")
            return False
        
        transactions = result.get('transactions', [])
        
        logger.info(f"\n=== FINAL RESULTS ===")
        logger.info(f"Total Transactions: {len(transactions)}")
        
        if not transactions:
            logger.error("❌ No transactions extracted!")
            return False
        
        # Analyze column separation quality
        analyze_column_separation(transactions)
        
        # Display sample transactions
        display_separated_transactions(transactions[:3])
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing column separation: {str(e)}")
        return False

def test_extraction_strategies(pdf_path: str):
    """
    Test different Tabula extraction strategies to see column separation
    """
    logger.info("\n=== TESTING EXTRACTION STRATEGIES ===")
    
    strategies = [
        {
            'name': 'HDFC Precise Columns',
            'params': {
                'pages': 'all',
                'multiple_tables': True,
                'lattice': True,
                'columns': [70, 200, 320, 380, 440, 500, 560],
                'pandas_options': {'header': None, 'dtype': str}
            }
        },
        {
            'name': 'Stream with Columns',
            'params': {
                'pages': 'all',
                'multiple_tables': True,
                'stream': True,
                'columns': [70, 200, 320, 380, 440, 500, 560],
                'pandas_options': {'header': None, 'dtype': str}
            }
        },
        {
            'name': 'Lattice Only',
            'params': {
                'pages': 'all',
                'multiple_tables': True,
                'lattice': True,
                'pandas_options': {'header': None, 'dtype': str}
            }
        },
        {
            'name': 'Default',
            'params': {
                'pages': 'all',
                'multiple_tables': True,
                'pandas_options': {'header': None, 'dtype': str}
            }
        }
    ]
    
    hdfc_processor = HDFCBankProcessor()
    
    for strategy in strategies:
        try:
            logger.info(f"\n--- {strategy['name']} Strategy ---")
            tables = tabula.read_pdf(pdf_path, **strategy['params'])
            logger.info(f"Extracted {len(tables)} tables")
            
            for i, table in enumerate(tables):
                if isinstance(table, pd.DataFrame) and not table.empty:
                    logger.info(f"Table {i+1}: Shape {table.shape}")
                    logger.info(f"Columns: {list(table.columns)}")
                    
                    # Check if data is properly separated
                    if table.shape[1] >= 5:
                        logger.info("✅ Good column separation")
                        logger.info(f"Sample row: {list(table.iloc[0])}")
                    elif table.shape[1] == 1:
                        logger.warning("❌ All data in single column")
                        logger.info(f"Sample data: {str(table.iloc[0, 0])[:100]}...")
                    else:
                        logger.warning(f"⚠️  Limited columns ({table.shape[1]})")
                    
                    # Test HDFC processing
                    transactions = hdfc_processor.process_hdfc_dataframe(table)
                    logger.info(f"Processed to {len(transactions)} transactions")
                    
                    if transactions:
                        sample_txn = transactions[0]
                        logger.info(f"Sample transaction fields:")
                        logger.info(f"  Date: '{sample_txn.get('date', 'MISSING')}'")
                        logger.info(f"  Narration: '{sample_txn.get('narration', 'MISSING')[:50]}...'")
                        logger.info(f"  Chq/Ref: '{sample_txn.get('chq_ref_no', 'MISSING')}'")
                        logger.info(f"  Amounts: W={sample_txn.get('withdrawal_amt')}, D={sample_txn.get('deposit_amt')}")
                    
                    logger.info("")
            
        except Exception as e:
            logger.error(f"{strategy['name']} failed: {str(e)}")

def analyze_column_separation(transactions):
    """
    Analyze if transactions have properly separated fields
    """
    logger.info("\n=== COLUMN SEPARATION ANALYSIS ===")
    
    total = len(transactions)
    
    # Check field separation quality
    properly_separated = 0
    merged_data_detected = 0
    
    for txn in transactions:
        narration = txn.get('narration', '')
        
        # Check if narration contains what looks like merged data
        if len(narration) > 200:  # Very long narration suggests merged data
            merged_data_detected += 1
        elif (txn.get('date') and 
              narration and len(narration) < 200 and
              (txn.get('withdrawal_amt') is not None or txn.get('deposit_amt') is not None)):
            properly_separated += 1
    
    logger.info(f"Column Separation Quality:")
    logger.info(f"  ✅ Properly separated: {properly_separated}/{total} ({properly_separated/total*100:.1f}%)")
    logger.info(f"  ❌ Merged data detected: {merged_data_detected}/{total} ({merged_data_detected/total*100:.1f}%)")
    
    # Check specific field completeness
    fields_analysis = {
        'Date': sum(1 for t in transactions if t.get('date')),
        'Narration': sum(1 for t in transactions if t.get('narration', '').strip()),
        'Chq./Ref.No.': sum(1 for t in transactions if t.get('chq_ref_no', '').strip()),
        'Value Dt': sum(1 for t in transactions if t.get('value_date', '').strip()),
        'Withdrawal': sum(1 for t in transactions if t.get('withdrawal_amt') is not None),
        'Deposit': sum(1 for t in transactions if t.get('deposit_amt') is not None),
        'Balance': sum(1 for t in transactions if t.get('closing_balance') is not None)
    }
    
    logger.info(f"\nField Completeness:")
    for field, count in fields_analysis.items():
        percentage = (count / total * 100) if total > 0 else 0
        status = "✅" if percentage > 80 else "⚠️" if percentage > 40 else "❌"
        logger.info(f"  {status} {field}: {count}/{total} ({percentage:.1f}%)")

def display_separated_transactions(transactions):
    """
    Display transactions to verify proper column separation
    """
    logger.info(f"\n=== SAMPLE SEPARATED TRANSACTIONS ===")
    
    for i, txn in enumerate(transactions):
        logger.info(f"\nTransaction {i+1}:")
        logger.info(f"  📅 Date: '{txn.get('date', 'MISSING')}'")
        logger.info(f"  📝 Narration: '{txn.get('narration', 'MISSING')}'")
        logger.info(f"  🔢 Chq./Ref.No.: '{txn.get('chq_ref_no', 'MISSING')}'")
        logger.info(f"  📅 Value Dt: '{txn.get('value_date', 'MISSING')}'")
        logger.info(f"  💸 Withdrawal: {txn.get('withdrawal_amt', 'MISSING')}")
        logger.info(f"  💰 Deposit: {txn.get('deposit_amt', 'MISSING')}")
        logger.info(f"  💳 Balance: {txn.get('closing_balance', 'MISSING')}")
        
        # Check if this looks like properly separated data
        narration = txn.get('narration', '')
        if len(narration) > 200:
            logger.warning(f"  ⚠️  Narration very long ({len(narration)} chars) - possible merged data")
        elif txn.get('date') and narration and len(narration) < 200:
            logger.info(f"  ✅ Looks properly separated")

def test_manual_column_splitting():
    """
    Test the manual column splitting functionality
    """
    logger.info("\n=== TESTING MANUAL COLUMN SPLITTING ===")
    
    # Create sample merged data
    merged_data = [
        "01/03/2025 UPI-KISHAN LAL BANG-**********@YBL-SBINO UPI123456 01/03/2025 10,200.00 0.00 2,71,128.46",
        "02/03/2025 NEFT PAYMENT FROM PHONEUP-MANSINGH RTGS789012 02/03/2025 0.00 5,000.00 2,76,128.46"
    ]
    
    # Create DataFrame with merged data
    df_merged = pd.DataFrame({0: merged_data})
    logger.info(f"Sample merged DataFrame shape: {df_merged.shape}")
    logger.info(f"Sample merged data:\n{df_merged}")
    
    # Test splitting
    hdfc_processor = HDFCBankProcessor()
    
    # Check if it detects merged data
    is_merged = hdfc_processor._is_data_merged_in_single_column(df_merged)
    logger.info(f"Merged data detection: {is_merged}")
    
    if is_merged:
        # Test splitting
        df_split = hdfc_processor._split_merged_column_data(df_merged)
        logger.info(f"After splitting shape: {df_split.shape}")
        logger.info(f"Split data:\n{df_split}")
        
        # Test processing
        transactions = hdfc_processor.process_hdfc_dataframe(df_split)
        logger.info(f"Processed to {len(transactions)} transactions")
        
        for i, txn in enumerate(transactions):
            logger.info(f"Transaction {i+1}: Date={txn.get('date')}, Narration='{txn.get('narration', '')[:50]}...', Amounts=W:{txn.get('withdrawal_amt')}/D:{txn.get('deposit_amt')}")

def main():
    """
    Main test function
    """
    if len(sys.argv) != 2:
        print("Usage: python test_column_separation.py <path_to_hdfc_pdf>")
        print("Example: python test_column_separation.py sample_hdfc_statement.pdf")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    logger.info("🔧 HDFC Bank Column Separation Test")
    logger.info("=" * 60)
    
    # Test manual splitting functionality
    test_manual_column_splitting()
    
    # Test with actual PDF
    success = test_column_separation(pdf_path)
    
    if success:
        logger.info("\n✅ Column separation test completed!")
        logger.info("Check the results above to verify proper column separation.")
        logger.info("If data is still merged, check the extraction strategy results.")
    else:
        logger.error("\n❌ Column separation test failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
