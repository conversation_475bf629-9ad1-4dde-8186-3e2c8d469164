#!/usr/bin/env python3
"""
Test script to validate HDFC Bank extraction accuracy
Compares extracted data with expected results for 100% accuracy validation
"""

import sys
import os
import logging
import pandas as pd
from pdf_processor import PDFProcessor
from hdfc_processor import HDFCBankProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_hdfc_extraction_accuracy(pdf_path: str):
    """
    Test HDFC Bank extraction accuracy
    """
    if not os.path.exists(pdf_path):
        logger.error(f"PDF file not found: {pdf_path}")
        return False
    
    try:
        logger.info(f"Testing HDFC extraction accuracy for: {pdf_path}")
        
        # Initialize processor
        processor = PDFProcessor()
        
        # Process the PDF
        result = processor.process_pdf(pdf_path)
        
        if not result.get('success', True):
            logger.error(f"Processing failed: {result.get('message', 'Unknown error')}")
            return False
        
        transactions = result.get('transactions', [])
        bank_name = result.get('bank_name', 'Unknown')
        extraction_method = result.get('extraction_method', 'Unknown')
        
        logger.info(f"Extraction Results:")
        logger.info(f"  Bank: {bank_name}")
        logger.info(f"  Method: {extraction_method}")
        logger.info(f"  Total Transactions: {len(transactions)}")
        
        if not transactions:
            logger.warning("No transactions extracted!")
            return False
        
        # Analyze extraction quality
        analyze_extraction_quality(transactions)
        
        # Display sample transactions for manual verification
        display_sample_transactions(transactions)
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing extraction: {str(e)}")
        return False

def analyze_extraction_quality(transactions):
    """
    Analyze the quality of extracted transactions
    """
    logger.info("\n=== EXTRACTION QUALITY ANALYSIS ===")
    
    total_transactions = len(transactions)
    
    # Check data completeness
    complete_dates = sum(1 for t in transactions if t.get('date'))
    complete_narrations = sum(1 for t in transactions if t.get('narration', '').strip())
    complete_amounts = sum(1 for t in transactions if t.get('withdrawal_amt') or t.get('deposit_amt'))
    complete_balances = sum(1 for t in transactions if t.get('closing_balance'))
    
    logger.info(f"Data Completeness:")
    logger.info(f"  Dates: {complete_dates}/{total_transactions} ({complete_dates/total_transactions*100:.1f}%)")
    logger.info(f"  Narrations: {complete_narrations}/{total_transactions} ({complete_narrations/total_transactions*100:.1f}%)")
    logger.info(f"  Amounts: {complete_amounts}/{total_transactions} ({complete_amounts/total_transactions*100:.1f}%)")
    logger.info(f"  Balances: {complete_balances}/{total_transactions} ({complete_balances/total_transactions*100:.1f}%)")
    
    # Check for data quality issues
    issues = []
    
    # Date validation
    invalid_dates = [t for t in transactions if not t.get('date')]
    if invalid_dates:
        issues.append(f"{len(invalid_dates)} transactions with invalid dates")
    
    # Amount validation
    zero_amounts = [t for t in transactions if not t.get('withdrawal_amt') and not t.get('deposit_amt')]
    if zero_amounts:
        issues.append(f"{len(zero_amounts)} transactions with no amounts")
    
    # Narration validation
    empty_narrations = [t for t in transactions if not t.get('narration', '').strip()]
    if empty_narrations:
        issues.append(f"{len(empty_narrations)} transactions with empty narrations")
    
    if issues:
        logger.warning("Data Quality Issues:")
        for issue in issues:
            logger.warning(f"  - {issue}")
    else:
        logger.info("✅ No data quality issues detected!")

def display_sample_transactions(transactions, sample_size=10):
    """
    Display sample transactions for manual verification
    """
    logger.info(f"\n=== SAMPLE TRANSACTIONS (First {sample_size}) ===")
    
    for i, txn in enumerate(transactions[:sample_size]):
        logger.info(f"\nTransaction {i+1}:")
        logger.info(f"  Date: {txn.get('date', 'N/A')}")
        logger.info(f"  Narration: {txn.get('narration', 'N/A')[:80]}...")
        logger.info(f"  Chq/Ref: {txn.get('chq_ref_no', 'N/A')}")
        logger.info(f"  Value Date: {txn.get('value_date', 'N/A')}")
        logger.info(f"  Withdrawal: {txn.get('withdrawal_amt', 'N/A')}")
        logger.info(f"  Deposit: {txn.get('deposit_amt', 'N/A')}")
        logger.info(f"  Balance: {txn.get('closing_balance', 'N/A')}")

def test_hdfc_processor_directly():
    """
    Test HDFC processor with sample data
    """
    logger.info("\n=== TESTING HDFC PROCESSOR DIRECTLY ===")
    
    # Create sample DataFrame that mimics HDFC format
    sample_data = {
        0: ['Date', '06/02/2025', '07/02/2025', '08/02/2025'],
        1: ['Narration', 'UPI-KISHAN LAL BANG-7793080931@YBL-SBINO', 'UPI-BAJRANG DARJI-BAJRANG.DARJI@YBL-SB', 'UPI SETTLEMENT EFR817-07/02/25'],
        2: ['Chq./Ref.No.', '-', '-', '-'],
        3: ['Value Dt', '06/02/2025', '07/02/2025', '08/02/2025'],
        4: ['Withdrawal Amt.', '10,200.00', '10,200.00', '5,120.00'],
        5: ['Deposit Amt.', '10,200.00', '10,200.00', '5,120.00'],
        6: ['Closing Balance', '2,71,128.46', '2,81,328.46', '2,86,448.46']
    }
    
    df = pd.DataFrame(sample_data)
    logger.info(f"Sample DataFrame:\n{df}")
    
    # Test HDFC processor
    hdfc_processor = HDFCBankProcessor()
    transactions = hdfc_processor.process_hdfc_dataframe(df)
    
    logger.info(f"Extracted {len(transactions)} transactions from sample data")
    
    for i, txn in enumerate(transactions):
        logger.info(f"Transaction {i+1}: {txn}")

def main():
    """
    Main test function
    """
    if len(sys.argv) != 2:
        print("Usage: python test_accuracy.py <path_to_hdfc_pdf>")
        print("Example: python test_accuracy.py sample_hdfc_statement.pdf")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    logger.info("🧪 HDFC Bank Extraction Accuracy Test")
    logger.info("=" * 50)
    
    # Test with actual PDF
    success = test_hdfc_extraction_accuracy(pdf_path)
    
    # Test processor directly
    test_hdfc_processor_directly()
    
    if success:
        logger.info("\n✅ Test completed successfully!")
        logger.info("Please manually verify the extracted data matches your PDF exactly.")
    else:
        logger.error("\n❌ Test failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
