#!/usr/bin/env python3
"""
Debug script to analyze transaction count issues in HDFC PDF processing
"""

import os
import sys
import logging
import pandas as pd
import tabula
from pdf_processor import PDFProcessor
from hdfc_processor import HDFCBankProcessor

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_transaction_extraction(pdf_file):
    """
    Debug the transaction extraction process step by step
    """
    print("=" * 80)
    print(f"DEBUGGING TRANSACTION EXTRACTION: {pdf_file}")
    print("=" * 80)
    
    if not os.path.exists(pdf_file):
        print(f"❌ File not found: {pdf_file}")
        return
    
    # Step 1: Test single strategy extraction
    print("\n🔍 STEP 1: Testing single extraction strategy")
    print("-" * 50)
    
    try:
        # Use only the primary strategy
        tables_primary = tabula.read_pdf(
            pdf_file,
            pages='all',
            multiple_tables=True,
            lattice=True,
            columns=[70, 200, 320, 380, 440, 500, 560],
            pandas_options={'header': None, 'dtype': str},
            silent=True
        )
        
        print(f"Primary strategy extracted {len(tables_primary)} tables")
        
        # Analyze each table
        total_rows = 0
        for i, table in enumerate(tables_primary):
            if isinstance(table, pd.DataFrame) and not table.empty:
                print(f"  Table {i+1}: {table.shape[0]} rows x {table.shape[1]} columns")
                total_rows += table.shape[0]
                
                # Show first few rows
                print(f"    First 3 rows:")
                for j, (idx, row) in enumerate(table.head(3).iterrows()):
                    row_str = " | ".join([str(val)[:20] for val in row.values])
                    print(f"      Row {j+1}: {row_str}")
        
        print(f"  Total raw rows across all tables: {total_rows}")
        
    except Exception as e:
        print(f"❌ Primary extraction failed: {str(e)}")
        return
    
    # Step 2: Test HDFC processor on each table
    print("\n🔍 STEP 2: Testing HDFC processor on each table")
    print("-" * 50)
    
    hdfc_processor = HDFCBankProcessor()
    all_transactions = []
    
    for i, table in enumerate(tables_primary):
        if isinstance(table, pd.DataFrame) and not table.empty:
            print(f"\n  Processing Table {i+1}:")
            
            # Process with HDFC processor
            transactions = hdfc_processor.process_hdfc_dataframe(table)
            print(f"    Extracted {len(transactions)} transactions")
            
            # Show sample transactions
            for j, txn in enumerate(transactions[:3]):
                date = txn.get('date', 'N/A')
                narration = str(txn.get('narration', 'N/A'))[:30]
                withdrawal = txn.get('withdrawal_amt', 0) or 0
                deposit = txn.get('deposit_amt', 0) or 0
                print(f"      Txn {j+1}: {date} | {narration}... | W:{withdrawal} D:{deposit}")
            
            all_transactions.extend(transactions)
    
    print(f"\n  Total transactions before deduplication: {len(all_transactions)}")
    
    # Step 3: Test deduplication
    print("\n🔍 STEP 3: Testing deduplication")
    print("-" * 50)
    
    processor = PDFProcessor()
    
    # Test old deduplication
    deduplicated_old = processor._deduplicate_transactions(all_transactions.copy())
    print(f"  Old deduplication: {len(all_transactions)} -> {len(deduplicated_old)} transactions")
    
    # Test new enhanced deduplication
    deduplicated_new = processor._deduplicate_transactions_enhanced(all_transactions.copy())
    print(f"  Enhanced deduplication: {len(all_transactions)} -> {len(deduplicated_new)} transactions")
    
    # Step 4: Compare with full processor
    print("\n🔍 STEP 4: Testing full processor")
    print("-" * 50)
    
    try:
        result = processor.process_pdf(pdf_file)
        if result['success']:
            final_count = len(result['data']['transactions'])
            print(f"  Full processor result: {final_count} transactions")
            
            # Quality check
            valid_count = 0
            for txn in result['data']['transactions']:
                if (txn.get('date') and 
                    txn.get('narration') and len(str(txn.get('narration')).strip()) > 2 and
                    (txn.get('withdrawal_amt') is not None or txn.get('deposit_amt') is not None)):
                    valid_count += 1
            
            print(f"  Valid transactions: {valid_count}/{final_count} ({valid_count/final_count*100:.1f}%)")
        else:
            print(f"  ❌ Full processor failed: {result.get('error')}")
    except Exception as e:
        print(f"  ❌ Full processor exception: {str(e)}")
    
    # Summary
    print("\n" + "=" * 80)
    print("SUMMARY:")
    print(f"  Raw table rows: {total_rows}")
    print(f"  Raw transactions: {len(all_transactions)}")
    print(f"  After old dedup: {len(deduplicated_old) if 'deduplicated_old' in locals() else 'N/A'}")
    print(f"  After enhanced dedup: {len(deduplicated_new) if 'deduplicated_new' in locals() else 'N/A'}")
    print(f"  Final result: {final_count if 'final_count' in locals() else 'N/A'}")
    print("=" * 80)

def main():
    """
    Main debug function
    """
    # Look for PDF files
    pdf_files = [f for f in os.listdir('.') if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        print("❌ No PDF files found in current directory")
        print("Please place HDFC bank statement PDF files in the backend directory")
        return
    
    print(f"Found PDF files: {pdf_files}")
    
    # Debug each file
    for pdf_file in pdf_files[:1]:  # Debug first file only
        debug_transaction_extraction(pdf_file)

if __name__ == "__main__":
    main()
