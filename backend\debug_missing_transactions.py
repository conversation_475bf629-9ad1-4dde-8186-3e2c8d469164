#!/usr/bin/env python3
"""
Debug script to identify why transactions are missing from HDFC PDF processing
"""

import sys
import os
import pandas as pd
import tabula
import logging
from pdf_processor import PDFProcessor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_missing_transactions(pdf_path: str):
    """
    Debug missing transactions by analyzing each step of the processing pipeline
    """
    print(f"\n🔍 DEBUGGING MISSING TRANSACTIONS")
    print(f"📄 PDF: {pdf_path}")
    print("=" * 80)
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return
    
    try:
        # Initialize processor
        processor = PDFProcessor()
        print("✅ PDF Processor initialized")
        
        # Step 1: Get page count
        try:
            import fitz
            doc = fitz.open(pdf_path)
            page_count = doc.page_count
            doc.close()
            print(f"📖 Total pages in PDF: {page_count}")
        except Exception as e:
            print(f"❌ Could not get page count: {str(e)}")
            page_count = "Unknown"
        
        # Step 2: Test raw table extraction
        print(f"\n🔧 STEP 1: Raw Table Extraction")
        print("-" * 40)
        
        # Extract all tables with different strategies
        strategies = [
            ("Default", {"pages": "all", "multiple_tables": True, "pandas_options": {"header": None}}),
            ("Lattice", {"pages": "all", "multiple_tables": True, "lattice": True, "pandas_options": {"header": None}}),
            ("Stream", {"pages": "all", "multiple_tables": True, "stream": True, "pandas_options": {"header": None}}),
        ]
        
        all_raw_tables = []
        for strategy_name, params in strategies:
            try:
                tables = tabula.read_pdf(pdf_path, **params)
                print(f"   {strategy_name}: {len(tables)} tables extracted")
                all_raw_tables.extend([(strategy_name, i, table) for i, table in enumerate(tables)])
                
                # Show table details
                for i, table in enumerate(tables):
                    if isinstance(table, pd.DataFrame) and not table.empty:
                        print(f"     Table {i+1}: Shape {table.shape}, Non-empty rows: {len(table.dropna(how='all'))}")
                    else:
                        print(f"     Table {i+1}: Empty or invalid")
                        
            except Exception as e:
                print(f"   {strategy_name}: Failed - {str(e)}")
        
        print(f"\n📊 Total raw tables extracted: {len(all_raw_tables)}")
        
        # Step 3: Test HDFC processing on each table
        print(f"\n🔧 STEP 2: HDFC Processing per Table")
        print("-" * 40)
        
        total_raw_transactions = 0
        table_transaction_counts = []
        
        for strategy, table_idx, table in all_raw_tables:
            if isinstance(table, pd.DataFrame) and not table.empty:
                print(f"\n--- Processing {strategy} Table {table_idx+1} ---")
                print(f"    Shape: {table.shape}")
                print(f"    Columns: {list(table.columns)}")
                print(f"    Non-empty rows: {len(table.dropna(how='all'))}")
                
                try:
                    # Process with HDFC processor
                    transactions = processor.hdfc_processor.process_hdfc_dataframe(table)
                    count = len(transactions)
                    total_raw_transactions += count
                    table_transaction_counts.append((strategy, table_idx+1, count))
                    
                    print(f"    ✅ Extracted: {count} transactions")
                    
                    if count > 0:
                        # Show sample transaction
                        sample = transactions[0]
                        print(f"    Sample: {sample.get('date')} - {sample.get('narration', '')[:50]}...")
                    
                except Exception as e:
                    print(f"    ❌ Processing failed: {str(e)}")
                    table_transaction_counts.append((strategy, table_idx+1, 0))
        
        print(f"\n📈 TRANSACTION COUNTS PER TABLE:")
        for strategy, table_num, count in table_transaction_counts:
            print(f"   {strategy} Table {table_num}: {count} transactions")
        print(f"   TOTAL RAW TRANSACTIONS: {total_raw_transactions}")
        
        # Step 4: Test full processing pipeline
        print(f"\n🔧 STEP 3: Full Processing Pipeline")
        print("-" * 40)
        
        try:
            result = processor.process_pdf(pdf_path)
            final_count = len(result.get('transactions', []))
            print(f"✅ Final result: {final_count} transactions")
            print(f"   Bank: {result.get('bank_name')}")
            print(f"   Method: {result.get('extraction_method')}")
            print(f"   Confidence: {result.get('detection_confidence', 0):.2f}")
            
            # Calculate loss
            if total_raw_transactions > 0:
                loss_count = total_raw_transactions - final_count
                loss_percentage = (loss_count / total_raw_transactions) * 100
                print(f"\n⚠️  TRANSACTION LOSS ANALYSIS:")
                print(f"   Raw transactions: {total_raw_transactions}")
                print(f"   Final transactions: {final_count}")
                print(f"   Lost transactions: {loss_count} ({loss_percentage:.1f}%)")
                
                if loss_count > 0:
                    print(f"\n🔍 LIKELY CAUSES OF LOSS:")
                    print(f"   1. Aggressive deduplication removing valid transactions")
                    print(f"   2. Strict validation rejecting valid transactions")
                    print(f"   3. Table extraction missing some pages/tables")
                    print(f"   4. Column mapping failures")
            
        except Exception as e:
            print(f"❌ Full processing failed: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "=" * 80)
        print("🎯 DEBUGGING COMPLETE")
        
    except Exception as e:
        print(f"❌ Debug failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python debug_missing_transactions.py <pdf_path>")
        print("Example: python debug_missing_transactions.py Acct.pdf")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    debug_missing_transactions(pdf_path)
