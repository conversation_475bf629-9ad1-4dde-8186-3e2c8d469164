#!/usr/bin/env python3
"""
Test script to verify the duplication fix for HDFC bank statement processing
"""

import os
import sys
import logging
from pdf_processor import PDFProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_duplication_fix():
    """
    Test the duplication fix with HDFC bank statements
    """
    print("=" * 80)
    print("HDFC BANK STATEMENT DUPLICATION FIX TEST")
    print("=" * 80)
    
    # Initialize processor
    processor = PDFProcessor()
    
    # Look for test PDF files
    test_files = []
    for file in os.listdir('.'):
        if file.lower().endswith('.pdf'):
            test_files.append(file)
    
    if not test_files:
        print("❌ No PDF files found in current directory")
        print("Please place HDFC bank statement PDF files in the backend directory")
        return False
    
    print(f"Found {len(test_files)} PDF file(s): {test_files}")
    print()
    
    all_tests_passed = True
    
    for pdf_file in test_files:
        print(f"🔍 Testing: {pdf_file}")
        print("-" * 60)
        
        try:
            # Process the PDF
            result = processor.process_pdf(pdf_file)
            
            if result.get('success'):
                transactions = result['data']['transactions']
                bank_name = result['data'].get('bank_name', 'Unknown')
                extraction_method = result['data'].get('extraction_method', 'Unknown')
                
                print(f"✅ Processing successful!")
                print(f"   Bank: {bank_name}")
                print(f"   Extraction method: {extraction_method}")
                print(f"   Total transactions: {len(transactions)}")
                
                # Analyze transaction quality
                valid_transactions = 0
                dates_found = 0
                narrations_found = 0
                amounts_found = 0
                
                for txn in transactions:
                    if txn.get('date'):
                        dates_found += 1
                    if txn.get('narration') and len(str(txn.get('narration')).strip()) > 2:
                        narrations_found += 1
                    if txn.get('withdrawal_amt') is not None or txn.get('deposit_amt') is not None:
                        amounts_found += 1
                    
                    # Count as valid if has date, narration, and amount
                    if (txn.get('date') and 
                        txn.get('narration') and len(str(txn.get('narration')).strip()) > 2 and
                        (txn.get('withdrawal_amt') is not None or txn.get('deposit_amt') is not None)):
                        valid_transactions += 1
                
                print(f"   Quality metrics:")
                print(f"     - Valid transactions: {valid_transactions}/{len(transactions)} ({valid_transactions/len(transactions)*100:.1f}%)")
                print(f"     - Transactions with dates: {dates_found}/{len(transactions)} ({dates_found/len(transactions)*100:.1f}%)")
                print(f"     - Transactions with narration: {narrations_found}/{len(transactions)} ({narrations_found/len(transactions)*100:.1f}%)")
                print(f"     - Transactions with amounts: {amounts_found}/{len(transactions)} ({amounts_found/len(transactions)*100:.1f}%)")
                
                # Show sample transactions
                print(f"   Sample transactions:")
                for i, txn in enumerate(transactions[:3]):
                    date = txn.get('date', 'N/A')
                    narration = str(txn.get('narration', 'N/A'))[:40]
                    withdrawal = txn.get('withdrawal_amt', 0) or 0
                    deposit = txn.get('deposit_amt', 0) or 0
                    balance = txn.get('closing_balance', 0) or 0
                    print(f"     {i+1}. {date} | {narration}... | W:{withdrawal} D:{deposit} B:{balance}")
                
                # Quality assessment
                if valid_transactions / len(transactions) >= 0.9:  # 90% valid transactions
                    print(f"   ✅ Quality: EXCELLENT (>90% valid transactions)")
                elif valid_transactions / len(transactions) >= 0.7:  # 70% valid transactions
                    print(f"   ⚠️  Quality: GOOD (70-90% valid transactions)")
                else:
                    print(f"   ❌ Quality: POOR (<70% valid transactions)")
                    all_tests_passed = False
                
            else:
                print(f"❌ Processing failed: {result.get('error', 'Unknown error')}")
                all_tests_passed = False
                
        except Exception as e:
            print(f"❌ Exception during processing: {str(e)}")
            all_tests_passed = False
        
        print()
    
    # Final summary
    print("=" * 80)
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED! Duplication fix appears to be working correctly.")
        print("   - Transaction counts should now match the original PDF")
        print("   - No excessive duplicates should be present")
        print("   - Header rows should be filtered out")
    else:
        print("❌ SOME TESTS FAILED! Please review the results above.")
        print("   - Check if transaction counts are still too high")
        print("   - Verify that duplicates are being removed properly")
        print("   - Ensure header rows are not being processed as transactions")
    print("=" * 80)
    
    return all_tests_passed

if __name__ == "__main__":
    test_duplication_fix()
