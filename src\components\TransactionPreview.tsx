import React, { useState } from 'react';
import { Transaction } from '../types';
import { Download, ArrowDownRight, ArrowUpRight, MoreVertical } from 'lucide-react';
import { format, parse } from 'date-fns';

interface TransactionPreviewProps {
  transactions: Transaction[];
  onGenerateXML: () => void;
  bankName: string;
  onTransactionsChange?: (updatedTransactions: Transaction[]) => void;
  detectionConfidence?: number;
  bankMetadata?: any;
}

const TransactionPreview: React.FC<TransactionPreviewProps> = ({
  transactions,
  onGenerateXML,
  bankName,
  onTransactionsChange,
  detectionConfidence,
  bankMetadata
}) => {
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const [editingTransaction, setEditingTransaction] = useState<Transaction | null>(null);
  const [transactionList, setTransactionList] = useState<Transaction[]>(transactions);

  const handleEdit = (transaction: Transaction) => {
    setEditingTransaction({ ...transaction });
  };

  const handleSaveEdit = (updatedTransaction: Transaction) => {
    const updatedList = transactionList.map(t => 
      t.id === updatedTransaction.id ? updatedTransaction : t
    );
    setTransactionList(updatedList);
    setEditingTransaction(null);
    onTransactionsChange?.(updatedList);
  };

  const handleCancelEdit = () => {
    setEditingTransaction(null);
  };

  const handleDelete = (transaction: Transaction) => {
    if (window.confirm('Are you sure you want to delete this transaction?')) {
      const updatedList = transactionList.filter(t => t.id !== transaction.id);
      setTransactionList(updatedList);
      onTransactionsChange?.(updatedList);
    }
  };
  
  const formatDate = (dateString: string) => {
    try {
      // Try to parse the date (handles different formats)
      let date: Date;
      
      if (dateString.includes('/')) {
        date = parse(dateString, 'dd/MM/yyyy', new Date());
      } else if (dateString.includes('-')) {
        const parts = dateString.split('-');
        if (parts[0].length === 4) {
          date = parse(dateString, 'yyyy-MM-dd', new Date());
        } else {
          date = parse(dateString, 'dd-MM-yyyy', new Date());
        }
      } else {
        date = new Date(dateString);
      }
      
      return format(date, 'dd MMM yyyy');
    } catch (e) {
      return dateString;
    }
  };
  
  const formatCurrency = (amount: number | null) => {
    if (amount === null) return '0.00';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };
  
  // Validation functions
  const isValidDate = (dateString: string): boolean => {
    const dateRegex = /^\d{2}\/\d{2}\/\d{2}$/;
    if (!dateRegex.test(dateString)) return false;
    
    const [day, month, year] = dateString.split('/').map(Number);
    const currentYear = new Date().getFullYear() % 100; // Get last 2 digits of current year
    return day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 0 && year <= currentYear;
  };
  
  const isValidAmount = (amount: number | null): boolean => {
    return amount !== null && !isNaN(amount) && amount >= 0;
  };
  
  const formatAmountForDisplay = (amount: number | null): string => {
    if (amount === null || amount === 0) return '0.00';
    return amount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  };
  
  const sortedTransactions = [...transactionList].sort((a, b) => {
    // Always sort by date in ascending order
    const dateA = new Date(a.date);
    const dateB = new Date(b.date);
    return dateA.getTime() - dateB.getTime();
  });
  
  // Calculate totals
  const totalDebit = transactionList.reduce((sum, t) => sum + (t.debit || 0), 0);
  const totalCredit = transactionList.reduce((sum, t) => sum + (t.credit || 0), 0);
  
  return (
    <div className="w-full px-4 py-6 space-y-6">
      {/* HDFC Bank Detection Status */}
      {bankName === 'HDFC' && detectionConfidence && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">
                HDFC Bank Statement Detected
              </h3>
              <div className="mt-2 text-sm text-green-700">
                <p>
                  Successfully identified as HDFC Bank statement with {(detectionConfidence * 100).toFixed(0)}% confidence.
                  {bankMetadata?.account_number && (
                    <span className="ml-2">Account: ****{bankMetadata.account_number.slice(-4)}</span>
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Transactions Preview
          </h3>
          <p className="text-gray-500">
            {transactions.length} transactions from {bankName} statement
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 text-sm">
            <span className="px-2 py-1 bg-green-50 text-green-700 rounded-md flex items-center">
              <ArrowDownRight className="w-3 h-3 mr-1" />
              In: {formatCurrency(totalCredit)}
            </span>
            <span className="px-2 py-1 bg-red-50 text-red-700 rounded-md flex items-center">
              <ArrowUpRight className="w-3 h-3 mr-1" />
              Out: {formatCurrency(totalDebit)}
            </span>
          </div>
          
          <button 
            className="btn btn-primary flex items-center"
            onClick={onGenerateXML}
          >
            <Download className="w-4 h-4 mr-2" />
            Generate XML
          </button>
        </div>
      </div>
      
      <div className="bg-white shadow-lg rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <div className="max-h-[70vh] overflow-y-auto">
            <table className="min-w-full divide-y divide-gray-200 table-auto">
              <thead className="bg-gradient-to-r from-gray-50 to-gray-100 sticky top-0 z-10 shadow-sm">
                <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28"
              >
                <div className="flex items-center">
                  Date
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[200px] max-w-[300px]"
              >
                <div className="flex items-center">
                  Narration
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32"
              >
                <div className="flex items-center">
                  Chq./Ref.No.
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28"
              >
                <div className="flex items-center">
                  Value Dt
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-36"
              >
                <div className="flex items-center justify-end">
                  Withdrawal Amt.
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-36"
              >
                <div className="flex items-center justify-end">
                  Deposit Amt.
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-40"
              >
                <div className="flex items-center justify-end">
                  Closing Balance
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-24"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedTransactions.map((transaction, index) => {
              const isDateValid = isValidDate(transaction.date);
              const isDebitValid = isValidAmount(transaction.debit);
              const isCreditValid = isValidAmount(transaction.credit);
              const isBalanceValid = isValidAmount(transaction.balance);
              const isEvenRow = index % 2 === 0;
              
              return (
                <tr key={transaction.id} className={`${isEvenRow ? 'bg-white' : 'bg-gray-50/50'} hover:bg-blue-50 transition-all duration-200 border-b border-gray-100`}>
                  <td className={`px-6 py-4 whitespace-nowrap text-sm ${
                    isDateValid ? 'text-gray-900' : 'text-red-600 bg-red-50'
                  }`}>
                    {editingTransaction?.id === transaction.id ? (
                      <input
                        type="text"
                        className="w-full px-2 py-1 border rounded"
                        value={editingTransaction.date}
                        onChange={(e) => setEditingTransaction({ ...editingTransaction, date: e.target.value })}
                      />
                    ) : (
                      formatDate(transaction.date)
                    )}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 min-w-[200px] max-w-[300px]">
                    {editingTransaction?.id === transaction.id ? (
                      <textarea
                        className="w-full px-2 py-1 border rounded resize-none min-h-[2.5rem]"
                        value={editingTransaction.description}
                        onChange={(e) => setEditingTransaction({ ...editingTransaction, description: e.target.value })}
                        rows={Math.max(2, Math.ceil(editingTransaction.description.length / 40))}
                      />
                    ) : (
                      <div className="whitespace-pre-wrap break-words leading-relaxed text-justify">
                        {transaction.description}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {editingTransaction?.id === transaction.id ? (
                      <input
                        type="text"
                        className="w-full px-2 py-1 border rounded"
                        value={editingTransaction.refNo || ''}
                        onChange={(e) => setEditingTransaction({ ...editingTransaction, refNo: e.target.value })}
                      />
                    ) : (
                      transaction.refNo || '-'
                    )}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {editingTransaction?.id === transaction.id ? (
                      <input
                        type="text"
                        className="w-full px-2 py-1 border rounded"
                        value={editingTransaction.valueDate || ''}
                        onChange={(e) => setEditingTransaction({ ...editingTransaction, valueDate: e.target.value })}
                      />
                    ) : (
                      transaction.valueDate ? formatDate(transaction.valueDate) : '-'
                    )}
                  </td>
                  <td className={`px-6 py-4 whitespace-nowrap text-sm text-right ${
                    isDebitValid ? 'text-red-600' : 'text-red-600 bg-red-50'
                  }`}>
                    {editingTransaction?.id === transaction.id ? (
                      <input
                        type="number"
                        className="w-full px-2 py-1 border rounded text-right"
                        value={editingTransaction.debit || ''}
                        onChange={(e) => setEditingTransaction({ ...editingTransaction, debit: parseFloat(e.target.value) || null })}
                      />
                    ) : (
                      transaction.debit ? formatAmountForDisplay(transaction.debit) : '0.00'
                    )}
                  </td>
                  <td className={`px-6 py-4 whitespace-nowrap text-sm text-right ${
                    isCreditValid ? 'text-green-600' : 'text-red-600 bg-red-50'
                  }`}>
                    {editingTransaction?.id === transaction.id ? (
                      <input
                        type="number"
                        className="w-full px-2 py-1 border rounded text-right"
                        value={editingTransaction.credit || ''}
                        onChange={(e) => setEditingTransaction({ ...editingTransaction, credit: parseFloat(e.target.value) || null })}
                      />
                    ) : (
                      transaction.credit ? formatAmountForDisplay(transaction.credit) : '0.00'
                    )}
                  </td>
                  <td className={`px-6 py-4 whitespace-nowrap text-sm text-right font-medium ${
                    isBalanceValid ? 'text-gray-900' : 'text-red-600 bg-red-50'
                  }`}>
                    {editingTransaction?.id === transaction.id ? (
                      <input
                        type="number"
                        className="w-full px-2 py-1 border rounded text-right"
                        value={editingTransaction.balance || ''}
                        onChange={(e) => setEditingTransaction({ ...editingTransaction, balance: parseFloat(e.target.value) || null })}
                      />
                    ) : (
                      transaction.balance ? formatAmountForDisplay(transaction.balance) : '0.00'
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-center relative">
                    {editingTransaction?.id === transaction.id ? (
                      <div className="flex items-center justify-center space-x-2">
                        <button
                          className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700"
                          onClick={() => handleSaveEdit(editingTransaction)}
                        >
                          Save
                        </button>
                        <button
                          className="px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                          onClick={handleCancelEdit}
                        >
                          Cancel
                        </button>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <button
                          className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                          onClick={() => setOpenMenuId(openMenuId === transaction.id ? null : transaction.id)}
                        >
                          <MoreVertical className="w-4 h-4 text-gray-500" />
                        </button>
                        {openMenuId === transaction.id && (
                          <>
                            <div 
                              className="fixed inset-0 z-10" 
                              onClick={() => setOpenMenuId(null)}
                            />
                            <div className="absolute right-0 mt-2 py-2 w-32 bg-white rounded-lg shadow-lg border border-gray-200 z-20">
                              <button
                                className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                                onClick={() => {
                                  handleEdit(transaction);
                                  setOpenMenuId(null);
                                }}
                              >
                                Edit
                              </button>
                              <button
                                className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100 flex items-center"
                                onClick={() => {
                                  handleDelete(transaction);
                                  setOpenMenuId(null);
                                }}
                              >
                                Delete
                              </button>
                            </div>
                          </>
                        )}
                      </div>
                    )}
                  </td>
                </tr>
              );
            })}
          </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransactionPreview;