#!/usr/bin/env python3
"""
Debug script for HDFC Bank extraction issues
Provides detailed analysis of table extraction and column mapping
"""

import sys
import os
import logging
import pandas as pd
import tabula
from pdf_processor import PDFProcessor
from hdfc_processor import HDFCBankProcessor

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_table_extraction(pdf_path: str):
    """
    Debug table extraction process step by step
    """
    logger.info(f"🔍 Debugging table extraction for: {pdf_path}")
    
    try:
        # Test different Tabula strategies
        strategies = [
            {
                'name': 'Default',
                'params': {
                    'pages': 'all',
                    'multiple_tables': True,
                    'pandas_options': {'header': None, 'dtype': str}
                }
            },
            {
                'name': 'Lattice',
                'params': {
                    'pages': 'all',
                    'multiple_tables': True,
                    'lattice': True,
                    'pandas_options': {'header': None, 'dtype': str}
                }
            },
            {
                'name': 'Stream',
                'params': {
                    'pages': 'all',
                    'multiple_tables': True,
                    'stream': True,
                    'pandas_options': {'header': None, 'dtype': str}
                }
            },
            {
                'name': 'Precise Columns',
                'params': {
                    'pages': 'all',
                    'multiple_tables': True,
                    'lattice': True,
                    'columns': [50, 150, 250, 350, 450, 550],
                    'pandas_options': {'header': None, 'dtype': str}
                }
            }
        ]
        
        all_tables = []
        
        for strategy in strategies:
            logger.info(f"\n--- Testing {strategy['name']} Strategy ---")
            try:
                tables = tabula.read_pdf(pdf_path, **strategy['params'])
                logger.info(f"Extracted {len(tables)} tables")
                
                for i, table in enumerate(tables):
                    if isinstance(table, pd.DataFrame) and not table.empty:
                        logger.info(f"Table {i+1} shape: {table.shape}")
                        logger.info(f"Table {i+1} preview:\n{table.head()}")
                        
                        # Test HDFC processing
                        hdfc_processor = HDFCBankProcessor()
                        transactions = hdfc_processor.process_hdfc_dataframe(table)
                        logger.info(f"Extracted {len(transactions)} transactions from table {i+1}")
                        
                        if transactions:
                            logger.info("Sample transaction:")
                            logger.info(f"  {transactions[0]}")
                
                all_tables.extend(tables)
                
            except Exception as e:
                logger.error(f"{strategy['name']} strategy failed: {str(e)}")
        
        return all_tables
        
    except Exception as e:
        logger.error(f"Debug extraction failed: {str(e)}")
        return []

def debug_column_mapping(df: pd.DataFrame):
    """
    Debug column mapping process
    """
    logger.info(f"\n🔍 Debugging column mapping for DataFrame shape: {df.shape}")
    
    hdfc_processor = HDFCBankProcessor()
    
    # Show raw data
    logger.info("Raw DataFrame:")
    logger.info(f"Columns: {list(df.columns)}")
    logger.info(f"Data:\n{df}")
    
    # Test column mapping
    column_mapping = hdfc_processor.map_hdfc_columns(df)
    logger.info(f"Column mapping result: {column_mapping}")
    
    # Analyze each column
    logger.info("\nColumn Analysis:")
    for col_idx in range(df.shape[1]):
        col_data = df.iloc[:, col_idx].astype(str).str.strip()
        non_empty = col_data[col_data != 'nan']
        
        logger.info(f"Column {col_idx}:")
        logger.info(f"  Sample values: {list(non_empty.head(5))}")
        
        # Test date parsing
        date_count = sum(1 for val in non_empty.head(10) if hdfc_processor._parse_hdfc_date(val))
        logger.info(f"  Valid dates: {date_count}/10")
        
        # Test amount parsing
        amount_count = sum(1 for val in non_empty.head(10) if hdfc_processor._parse_hdfc_amount(val) is not None)
        logger.info(f"  Valid amounts: {amount_count}/10")
        
        # Text analysis
        avg_length = non_empty.str.len().mean() if len(non_empty) > 0 else 0
        logger.info(f"  Average text length: {avg_length:.1f}")

def debug_transaction_creation(df: pd.DataFrame, column_mapping: dict):
    """
    Debug transaction creation process
    """
    logger.info(f"\n🔍 Debugging transaction creation")
    
    hdfc_processor = HDFCBankProcessor()
    
    # Process each row individually
    for idx in range(min(5, df.shape[0])):  # Test first 5 rows
        logger.info(f"\n--- Processing Row {idx} ---")
        row = df.iloc[idx]
        logger.info(f"Raw row data: {list(row)}")
        
        try:
            transaction = hdfc_processor._create_hdfc_transaction_accurate(row, column_mapping, idx)
            if transaction:
                logger.info(f"✅ Transaction created: {transaction}")
            else:
                logger.warning(f"❌ No transaction created for row {idx}")
                
                # Debug why transaction wasn't created
                date_col = column_mapping.get('date')
                if date_col is not None:
                    date_str = str(row.iloc[date_col]).strip()
                    parsed_date = hdfc_processor._parse_hdfc_date(date_str)
                    logger.info(f"  Date parsing: '{date_str}' -> {parsed_date}")
                
                narration_col = column_mapping.get('narration')
                if narration_col is not None:
                    narration = str(row.iloc[narration_col]).strip()
                    logger.info(f"  Narration: '{narration}'")
                
        except Exception as e:
            logger.error(f"❌ Error creating transaction for row {idx}: {str(e)}")

def main():
    """
    Main debug function
    """
    if len(sys.argv) != 2:
        print("Usage: python debug_extraction.py <path_to_hdfc_pdf>")
        print("Example: python debug_extraction.py sample_hdfc_statement.pdf")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    if not os.path.exists(pdf_path):
        logger.error(f"PDF file not found: {pdf_path}")
        sys.exit(1)
    
    logger.info("🐛 HDFC Bank Extraction Debug Tool")
    logger.info("=" * 50)
    
    # Debug table extraction
    tables = debug_table_extraction(pdf_path)
    
    if not tables:
        logger.error("No tables extracted for debugging")
        sys.exit(1)
    
    # Debug the first non-empty table
    for table in tables:
        if isinstance(table, pd.DataFrame) and not table.empty:
            logger.info(f"\n🔍 Detailed analysis of table with shape {table.shape}")
            
            # Debug column mapping
            debug_column_mapping(table)
            
            # Test column mapping
            hdfc_processor = HDFCBankProcessor()
            column_mapping = hdfc_processor.map_hdfc_columns(table)
            
            if column_mapping:
                # Debug transaction creation
                debug_transaction_creation(table, column_mapping)
            else:
                logger.warning("No column mapping found for detailed transaction debugging")
            
            break
    
    logger.info("\n✅ Debug analysis complete!")
    logger.info("Check the logs above to identify extraction issues.")

if __name__ == "__main__":
    main()
