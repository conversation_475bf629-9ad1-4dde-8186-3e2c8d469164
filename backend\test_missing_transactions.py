#!/usr/bin/env python3
"""
Test script to identify why transactions are missing
Comprehensive analysis to find where transactions are lost in the extraction process
"""

import sys
import os
import logging
import pandas as pd
import tabula
from pdf_processor import PDFProcessor
from hdfc_processor import HDFCBankProcessor

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_missing_transactions(pdf_path: str, expected_count: int = None):
    """
    Comprehensive test to identify missing transactions
    """
    if not os.path.exists(pdf_path):
        logger.error(f"PDF file not found: {pdf_path}")
        return False
    
    try:
        logger.info(f"🔍 ANALYZING MISSING TRANSACTIONS")
        logger.info(f"PDF: {pdf_path}")
        if expected_count:
            logger.info(f"Expected transactions: {expected_count}")
        logger.info("=" * 80)
        
        # Step 1: Test raw table extraction
        raw_table_analysis(pdf_path)
        
        # Step 2: Test HDFC processing
        hdfc_processing_analysis(pdf_path)
        
        # Step 3: Test full pipeline
        full_pipeline_analysis(pdf_path, expected_count)
        
        return True
        
    except Exception as e:
        logger.error(f"Error in missing transaction analysis: {str(e)}")
        return False

def raw_table_analysis(pdf_path: str):
    """
    Analyze raw table extraction to see how many tables and rows we get
    """
    logger.info("\n📊 STEP 1: RAW TABLE EXTRACTION ANALYSIS")
    logger.info("-" * 50)
    
    strategies = [
        {
            'name': 'HDFC Precise',
            'params': {
                'pages': 'all',
                'multiple_tables': True,
                'lattice': True,
                'columns': [70, 200, 320, 380, 440, 500, 560],
                'pandas_options': {'header': None, 'dtype': str}
            }
        },
        {
            'name': 'Stream Columns',
            'params': {
                'pages': 'all',
                'multiple_tables': True,
                'stream': True,
                'columns': [70, 200, 320, 380, 440, 500, 560],
                'pandas_options': {'header': None, 'dtype': str}
            }
        },
        {
            'name': 'Lattice Only',
            'params': {
                'pages': 'all',
                'multiple_tables': True,
                'lattice': True,
                'pandas_options': {'header': None, 'dtype': str}
            }
        },
        {
            'name': 'Default',
            'params': {
                'pages': 'all',
                'multiple_tables': True,
                'pandas_options': {'header': None, 'dtype': str}
            }
        }
    ]
    
    all_tables = []
    total_rows = 0
    
    for strategy in strategies:
        try:
            logger.info(f"\n🔧 Testing {strategy['name']} strategy:")
            tables = tabula.read_pdf(pdf_path, **strategy['params'])
            logger.info(f"  Extracted {len(tables)} tables")
            
            strategy_rows = 0
            for i, table in enumerate(tables):
                if isinstance(table, pd.DataFrame) and not table.empty:
                    rows = table.shape[0]
                    cols = table.shape[1]
                    strategy_rows += rows
                    logger.info(f"    Table {i+1}: {rows} rows × {cols} cols")
                    
                    # Show sample data
                    if rows > 0:
                        sample_row = table.iloc[0]
                        logger.debug(f"    Sample: {list(sample_row)}")
            
            logger.info(f"  Total rows in strategy: {strategy_rows}")
            total_rows += strategy_rows
            all_tables.extend(tables)
            
        except Exception as e:
            logger.error(f"  {strategy['name']} failed: {str(e)}")
    
    logger.info(f"\n📈 RAW EXTRACTION SUMMARY:")
    logger.info(f"  Total tables extracted: {len(all_tables)}")
    logger.info(f"  Total rows extracted: {total_rows}")
    logger.info(f"  Average rows per table: {total_rows/len(all_tables) if all_tables else 0:.1f}")

def hdfc_processing_analysis(pdf_path: str):
    """
    Analyze HDFC processing to see how many transactions are created from tables
    """
    logger.info("\n🏦 STEP 2: HDFC PROCESSING ANALYSIS")
    logger.info("-" * 50)
    
    # Extract tables using best strategy
    try:
        tables = tabula.read_pdf(
            pdf_path,
            pages='all',
            multiple_tables=True,
            lattice=True,
            columns=[70, 200, 320, 380, 440, 500, 560],
            pandas_options={'header': None, 'dtype': str}
        )
        
        logger.info(f"Using {len(tables)} tables for HDFC processing analysis")
        
        hdfc_processor = HDFCBankProcessor()
        total_transactions = 0
        
        for i, table in enumerate(tables):
            if isinstance(table, pd.DataFrame) and not table.empty:
                logger.info(f"\n📋 Processing Table {i+1}:")
                logger.info(f"  Shape: {table.shape}")
                logger.info(f"  Columns: {list(table.columns)}")
                
                # Show raw data sample
                logger.debug(f"  Raw data sample:\n{table.head(3)}")
                
                # Test column mapping
                column_mapping = hdfc_processor.map_hdfc_columns(table)
                logger.info(f"  Column mapping: {column_mapping}")
                
                # Process transactions
                transactions = hdfc_processor.process_hdfc_dataframe(table)
                logger.info(f"  Transactions extracted: {len(transactions)}")
                total_transactions += len(transactions)
                
                # Show sample transactions
                if transactions:
                    sample_txn = transactions[0]
                    logger.info(f"  Sample transaction:")
                    logger.info(f"    Date: {sample_txn.get('date')}")
                    logger.info(f"    Narration: {sample_txn.get('narration', '')[:50]}...")
                    logger.info(f"    Amounts: W={sample_txn.get('withdrawal_amt')}, D={sample_txn.get('deposit_amt')}")
                
                # Analyze why some rows might not become transactions
                if len(transactions) < table.shape[0]:
                    logger.warning(f"  ⚠️  Only {len(transactions)}/{table.shape[0]} rows became transactions")
                    analyze_skipped_rows(table, transactions, hdfc_processor)
        
        logger.info(f"\n📊 HDFC PROCESSING SUMMARY:")
        logger.info(f"  Total transactions from all tables: {total_transactions}")
        
    except Exception as e:
        logger.error(f"HDFC processing analysis failed: {str(e)}")

def analyze_skipped_rows(table: pd.DataFrame, transactions: list, hdfc_processor):
    """
    Analyze why some table rows didn't become transactions
    """
    logger.info(f"    🔍 Analyzing {table.shape[0] - len(transactions)} skipped rows:")
    
    # Get column mapping
    column_mapping = hdfc_processor.map_hdfc_columns(table)
    
    # Check each row
    for idx in range(table.shape[0]):
        row = table.iloc[idx]
        
        # Try to create transaction
        try:
            transaction = hdfc_processor._create_hdfc_transaction_accurate(row, column_mapping, idx)
            if not transaction:
                # Analyze why it failed
                date_col = column_mapping.get('date')
                if date_col is not None and date_col < len(row):
                    date_str = str(row.iloc[date_col]).strip()
                    parsed_date = hdfc_processor._parse_hdfc_date(date_str)
                    
                    if not parsed_date:
                        logger.debug(f"      Row {idx}: Invalid date '{date_str}'")
                    else:
                        logger.debug(f"      Row {idx}: Valid date but failed other validation")
                        logger.debug(f"        Row data: {list(row)}")
                else:
                    logger.debug(f"      Row {idx}: No date column mapped")
        except Exception as e:
            logger.debug(f"      Row {idx}: Exception during processing: {str(e)}")

def full_pipeline_analysis(pdf_path: str, expected_count: int = None):
    """
    Test the full pipeline to see final transaction count
    """
    logger.info("\n🔄 STEP 3: FULL PIPELINE ANALYSIS")
    logger.info("-" * 50)
    
    try:
        processor = PDFProcessor()
        result = processor.process_pdf(pdf_path)
        
        if not result.get('success', True):
            logger.error(f"Pipeline failed: {result.get('message', 'Unknown error')}")
            return
        
        transactions = result.get('transactions', [])
        extraction_method = result.get('extraction_method', 'Unknown')
        
        logger.info(f"📊 FINAL RESULTS:")
        logger.info(f"  Extraction method: {extraction_method}")
        logger.info(f"  Final transaction count: {len(transactions)}")
        
        if expected_count:
            missing = expected_count - len(transactions)
            logger.info(f"  Expected: {expected_count}")
            logger.info(f"  Missing: {missing} ({missing/expected_count*100:.1f}%)")
            
            if missing > 0:
                logger.warning(f"  ❌ {missing} transactions are missing!")
            else:
                logger.info(f"  ✅ All transactions captured!")
        
        # Analyze transaction distribution
        if transactions:
            dates = [t.get('date') for t in transactions if t.get('date')]
            if dates:
                logger.info(f"  Date range: {min(dates)} to {max(dates)}")
            
            # Check for gaps in dates
            unique_dates = sorted(set(dates))
            logger.info(f"  Unique transaction dates: {len(unique_dates)}")
            
            # Sample transactions
            logger.info(f"  Sample transactions:")
            for i, txn in enumerate(transactions[:3]):
                logger.info(f"    {i+1}. {txn.get('date')} - {txn.get('narration', '')[:50]}...")
        
    except Exception as e:
        logger.error(f"Full pipeline analysis failed: {str(e)}")

def main():
    """
    Main test function
    """
    if len(sys.argv) < 2:
        print("Usage: python test_missing_transactions.py <path_to_hdfc_pdf> [expected_count]")
        print("Example: python test_missing_transactions.py statement.pdf 87")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    expected_count = int(sys.argv[2]) if len(sys.argv) > 2 else None
    
    logger.info("🔍 HDFC Bank Missing Transactions Analysis")
    logger.info("=" * 80)
    
    success = test_missing_transactions(pdf_path, expected_count)
    
    if success:
        logger.info("\n✅ Analysis completed!")
        logger.info("Check the detailed logs above to identify where transactions are being lost.")
        if expected_count:
            logger.info(f"If you're still missing transactions from the expected {expected_count}, ")
            logger.info("the logs will show exactly where they're being filtered out.")
    else:
        logger.error("\n❌ Analysis failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
