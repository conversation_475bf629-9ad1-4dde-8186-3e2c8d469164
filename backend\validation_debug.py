#!/usr/bin/env python3
"""
Debug validation filtering to see what transactions are being rejected
"""

import sys
import os
import logging
import pandas as pd

# Disable excessive debug logging from PDF libraries
logging.getLogger('pdfminer').setLevel(logging.WARNING)
logging.getLogger('pdfplumber').setLevel(logging.WARNING)
logging.getLogger('tabula').setLevel(logging.WARNING)

# Configure our logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_validation(pdf_path: str):
    """
    Debug validation filtering to see what's being rejected
    """
    if not os.path.exists(pdf_path):
        logger.error(f"PDF file not found: {pdf_path}")
        return False

    try:
        from pdf_processor import PDFProcessor

        logger.info(f"🔍 DEBUGGING VALIDATION FILTERING")
        logger.info(f"PDF: {pdf_path}")
        logger.info("=" * 60)

        # Create processor and extract transactions before validation
        processor = PDFProcessor()

        # We need to monkey patch the validation method to see what's being filtered
        original_is_valid = processor._is_valid_transaction_row

        rejected_transactions = []
        accepted_transactions = []

        def debug_is_valid(txn):
            result = original_is_valid(txn)
            if result:
                accepted_transactions.append(txn)
            else:
                rejected_transactions.append(txn)
            return result

        # Replace the validation method
        processor._is_valid_transaction_row = debug_is_valid

        # Process the PDF
        result = processor.process_pdf(pdf_path)
        final_transactions = result.get('transactions', [])

        logger.info(f"\n📊 VALIDATION RESULTS:")
        logger.info(f"Accepted transactions: {len(accepted_transactions)}")
        logger.info(f"Rejected transactions: {len(rejected_transactions)}")
        logger.info(f"Final transactions: {len(final_transactions)}")

        if rejected_transactions:
            logger.info(f"\n❌ REJECTED TRANSACTIONS ANALYSIS:")

            # Analyze rejection reasons
            no_date = 0
            no_amount = 0
            no_narration = 0
            header_pattern = 0

            for txn in rejected_transactions:
                narration = str(txn.get('narration', txn.get('description', '')) or '').strip().lower()
                date = str(txn.get('date', '')).strip()
                withdrawal = txn.get('withdrawal_amt', txn.get('debit'))
                deposit = txn.get('deposit_amt', txn.get('credit'))

                # Check rejection reasons
                header_patterns = [
                    'date', 'narration', 'description', 'particulars', 'details',
                    'chq', 'ref', 'value', 'withdrawal', 'deposit', 'balance',
                    'opening', 'closing', 'total', 'carried', 'forward',
                    'brought', 'statement', 'account', 'period'
                ]

                if any(pattern in narration for pattern in header_patterns):
                    header_pattern += 1
                elif not date or len(date) < 6:
                    no_date += 1
                elif not withdrawal and not deposit:
                    no_amount += 1
                elif not narration or len(narration) < 2:
                    no_narration += 1

            logger.info(f"  Header patterns: {header_pattern}")
            logger.info(f"  No/invalid date: {no_date}")
            logger.info(f"  No amount: {no_amount}")
            logger.info(f"  No narration: {no_narration}")

            # Show some examples of rejected transactions
            logger.info(f"\n🔍 SAMPLE REJECTED TRANSACTIONS:")
            for i, txn in enumerate(rejected_transactions[:10]):
                logger.info(f"  {i+1}: Date='{txn.get('date', '')}', Narration='{txn.get('narration', txn.get('description', ''))}', Withdrawal='{txn.get('withdrawal_amt', txn.get('debit', ''))}', Deposit='{txn.get('deposit_amt', txn.get('credit', ''))}'")

        return True

    except Exception as e:
        logger.error(f"Error in validation debug: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python validation_debug.py <pdf_path>")
        sys.exit(1)

    pdf_path = sys.argv[1]
    success = debug_validation(pdf_path)
    sys.exit(0 if success else 1)
