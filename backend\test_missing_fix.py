#!/usr/bin/env python3
"""
Test script to verify that the missing transactions fix works correctly
"""

import sys
import os
import pandas as pd
import tabula
import logging
from pdf_processor import PDFProcessor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_missing_transactions_fix(pdf_path: str):
    """
    Test that all transactions are extracted and no pages are missing
    """
    print(f"\n🧪 TESTING MISSING TRANSACTIONS FIX")
    print(f"📄 PDF: {pdf_path}")
    print("=" * 80)
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return False
    
    try:
        # Initialize processor
        processor = PDFProcessor()
        print("✅ PDF Processor initialized")
        
        # Step 1: Get baseline metrics
        print(f"\n📊 STEP 1: Baseline Metrics")
        print("-" * 40)
        
        # Get page count
        try:
            import fitz
            doc = fitz.open(pdf_path)
            page_count = doc.page_count
            doc.close()
            print(f"📖 Total pages in PDF: {page_count}")
        except Exception as e:
            print(f"❌ Could not get page count: {str(e)}")
            page_count = "Unknown"
        
        # Step 2: Test raw extraction capacity
        print(f"\n🔧 STEP 2: Raw Extraction Capacity")
        print("-" * 40)
        
        # Test different extraction strategies
        strategies = [
            ("Tabula Default", {"pages": "all", "multiple_tables": True, "pandas_options": {"header": None}}),
            ("Tabula Lattice", {"pages": "all", "multiple_tables": True, "lattice": True, "pandas_options": {"header": None}}),
            ("Tabula Stream", {"pages": "all", "multiple_tables": True, "stream": True, "pandas_options": {"header": None}}),
        ]
        
        max_raw_transactions = 0
        best_strategy = None
        
        for strategy_name, params in strategies:
            try:
                tables = tabula.read_pdf(pdf_path, **params)
                strategy_transactions = 0
                
                for table in tables:
                    if isinstance(table, pd.DataFrame) and not table.empty:
                        # Quick count of potential transaction rows
                        non_empty_rows = len(table.dropna(how='all'))
                        # Estimate transactions (subtract likely header rows)
                        estimated_transactions = max(0, non_empty_rows - 2)
                        strategy_transactions += estimated_transactions
                
                print(f"   {strategy_name}: ~{strategy_transactions} potential transactions")
                
                if strategy_transactions > max_raw_transactions:
                    max_raw_transactions = strategy_transactions
                    best_strategy = strategy_name
                    
            except Exception as e:
                print(f"   {strategy_name}: Failed - {str(e)}")
        
        print(f"   Best raw extraction: {best_strategy} with ~{max_raw_transactions} potential transactions")
        
        # Step 3: Test our fixed processing
        print(f"\n🔧 STEP 3: Fixed Processing Pipeline")
        print("-" * 40)
        
        try:
            result = processor.process_pdf(pdf_path)
            final_count = len(result.get('transactions', []))
            
            print(f"✅ Final result: {final_count} transactions")
            print(f"   Bank: {result.get('bank_name')}")
            print(f"   Method: {result.get('extraction_method')}")
            print(f"   Confidence: {result.get('detection_confidence', 0):.2f}")
            
            # Calculate extraction efficiency
            if max_raw_transactions > 0:
                efficiency = (final_count / max_raw_transactions) * 100
                print(f"   Extraction efficiency: {efficiency:.1f}%")
                
                # Success criteria
                success_criteria = [
                    (final_count > 0, "At least some transactions extracted"),
                    (efficiency > 50, f"Extraction efficiency > 50% (got {efficiency:.1f}%)"),
                    (final_count >= max_raw_transactions * 0.8, f"At least 80% of potential transactions (got {final_count}/{max_raw_transactions})")
                ]
                
                print(f"\n🎯 SUCCESS CRITERIA:")
                all_passed = True
                for passed, description in success_criteria:
                    status = "✅ PASS" if passed else "❌ FAIL"
                    print(f"   {status}: {description}")
                    if not passed:
                        all_passed = False
                
                # Show sample transactions
                if final_count > 0:
                    print(f"\n📋 SAMPLE TRANSACTIONS:")
                    transactions = result.get('transactions', [])
                    for i, txn in enumerate(transactions[:3]):
                        print(f"   {i+1}. {txn.get('date')} - {txn.get('narration', '')[:50]}... - W:{txn.get('withdrawal_amt')} D:{txn.get('deposit_amt')} B:{txn.get('closing_balance')}")
                    
                    if final_count > 3:
                        print(f"   ... and {final_count - 3} more transactions")
                
                return all_passed
            else:
                print(f"❌ No baseline to compare against")
                return False
            
        except Exception as e:
            print(f"❌ Processing failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_pdfs():
    """
    Test multiple PDFs if available
    """
    print(f"\n🧪 TESTING MULTIPLE PDFs")
    print("=" * 80)
    
    # Look for PDF files in current directory
    pdf_files = [f for f in os.listdir('.') if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        print("❌ No PDF files found in current directory")
        return False
    
    print(f"📄 Found {len(pdf_files)} PDF files: {pdf_files}")
    
    results = []
    for pdf_file in pdf_files:
        print(f"\n" + "="*60)
        print(f"Testing: {pdf_file}")
        print("="*60)
        
        success = test_missing_transactions_fix(pdf_file)
        results.append((pdf_file, success))
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("=" * 80)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for pdf_file, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status}: {pdf_file}")
    
    print(f"\nOverall: {passed}/{total} PDFs passed the test")
    return passed == total

if __name__ == "__main__":
    if len(sys.argv) == 2:
        # Test specific PDF
        pdf_path = sys.argv[1]
        success = test_missing_transactions_fix(pdf_path)
        print(f"\n🎯 RESULT: {'SUCCESS' if success else 'FAILED'}")
        sys.exit(0 if success else 1)
    else:
        # Test all PDFs in directory
        success = test_multiple_pdfs()
        print(f"\n🎯 OVERALL RESULT: {'SUCCESS' if success else 'FAILED'}")
        sys.exit(0 if success else 1)
