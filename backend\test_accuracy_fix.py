#!/usr/bin/env python3
"""
Test script to verify the accuracy fix for HDFC bank statement processing
"""

import os
import sys
import logging
import pandas as pd
import tabula
from pdf_processor import PDFProcessor
from hdfc_processor import HDFCBankProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_accuracy_improvements():
    """
    Test the accuracy improvements with HDFC bank statements
    """
    print("=" * 80)
    print("HDFC BANK STATEMENT ACCURACY FIX TEST")
    print("=" * 80)
    
    # Initialize processor
    processor = PDFProcessor()
    
    # Look for test PDF files
    test_files = []
    for file in os.listdir('.'):
        if file.lower().endswith('.pdf'):
            test_files.append(file)
    
    if not test_files:
        print("❌ No PDF files found in current directory")
        print("Please place HDFC bank statement PDF files in the backend directory")
        return False
    
    print(f"Found {len(test_files)} PDF file(s): {test_files}")
    print()
    
    all_tests_passed = True
    
    for pdf_file in test_files:
        print(f"🔍 Testing Accuracy: {pdf_file}")
        print("-" * 60)
        
        try:
            # Process the PDF
            result = processor.process_pdf(pdf_file)
            
            if result.get('success'):
                transactions = result['data']['transactions']
                bank_name = result['data'].get('bank_name', 'Unknown')
                extraction_method = result['data'].get('extraction_method', 'Unknown')
                
                print(f"✅ Processing successful!")
                print(f"   Bank: {bank_name}")
                print(f"   Extraction method: {extraction_method}")
                print(f"   Total transactions: {len(transactions)}")
                
                # Analyze data accuracy
                date_accuracy = 0
                narration_accuracy = 0
                amount_accuracy = 0
                complete_transactions = 0
                
                for i, txn in enumerate(transactions[:10]):  # Check first 10 transactions
                    # Check date format
                    if txn.get('date') and len(txn.get('date', '')) == 10:  # YYYY-MM-DD format
                        date_accuracy += 1
                    
                    # Check narration quality
                    narration = txn.get('narration', '')
                    if narration and len(narration) > 5 and narration not in ['nan', 'None', '']:
                        narration_accuracy += 1
                    
                    # Check amount accuracy
                    withdrawal = txn.get('withdrawal_amt')
                    deposit = txn.get('deposit_amt')
                    balance = txn.get('closing_balance')
                    
                    if (withdrawal is not None or deposit is not None) and balance is not None:
                        amount_accuracy += 1
                    
                    # Check complete transaction
                    if (txn.get('date') and 
                        txn.get('narration') and 
                        (withdrawal is not None or deposit is not None) and
                        balance is not None):
                        complete_transactions += 1
                
                sample_size = min(10, len(transactions))
                
                print(f"   Accuracy Analysis (first {sample_size} transactions):")
                print(f"     - Date accuracy: {date_accuracy}/{sample_size} ({date_accuracy/sample_size*100:.1f}%)")
                print(f"     - Narration accuracy: {narration_accuracy}/{sample_size} ({narration_accuracy/sample_size*100:.1f}%)")
                print(f"     - Amount accuracy: {amount_accuracy}/{sample_size} ({amount_accuracy/sample_size*100:.1f}%)")
                print(f"     - Complete transactions: {complete_transactions}/{sample_size} ({complete_transactions/sample_size*100:.1f}%)")
                
                # Show sample transactions with exact data
                print(f"   Sample transactions (exact data from PDF):")
                for i, txn in enumerate(transactions[:3]):
                    date = txn.get('date', 'N/A')
                    narration = str(txn.get('narration', 'N/A'))[:40]
                    chq_ref = txn.get('chq_ref_no', '')
                    value_date = txn.get('value_date', '')
                    withdrawal = txn.get('withdrawal_amt', 0) or 0
                    deposit = txn.get('deposit_amt', 0) or 0
                    balance = txn.get('closing_balance', 0) or 0
                    
                    print(f"     {i+1}. Date: {date}")
                    print(f"        Narration: {narration}...")
                    print(f"        Chq/Ref: {chq_ref}")
                    print(f"        Value Date: {value_date}")
                    print(f"        Withdrawal: {withdrawal}")
                    print(f"        Deposit: {deposit}")
                    print(f"        Balance: {balance}")
                    print()
                
                # Overall accuracy assessment
                overall_accuracy = (date_accuracy + narration_accuracy + amount_accuracy + complete_transactions) / (4 * sample_size) * 100
                
                if overall_accuracy >= 90:
                    print(f"   ✅ Accuracy: EXCELLENT ({overall_accuracy:.1f}% - matches PDF exactly)")
                elif overall_accuracy >= 75:
                    print(f"   ⚠️  Accuracy: GOOD ({overall_accuracy:.1f}% - minor discrepancies)")
                else:
                    print(f"   ❌ Accuracy: POOR ({overall_accuracy:.1f}% - significant data misalignment)")
                    all_tests_passed = False
                
                # Test specific HDFC columns
                print(f"   Column Mapping Test:")
                has_all_columns = True
                required_columns = ['date', 'narration', 'withdrawal_amt', 'deposit_amt', 'closing_balance']
                
                for col in required_columns:
                    sample_txn = transactions[0] if transactions else {}
                    if col in sample_txn and sample_txn[col] is not None:
                        print(f"     ✅ {col}: Present")
                    else:
                        print(f"     ❌ {col}: Missing")
                        has_all_columns = False
                
                if has_all_columns:
                    print(f"   ✅ All required HDFC columns mapped correctly")
                else:
                    print(f"   ❌ Some HDFC columns missing - data misalignment detected")
                    all_tests_passed = False
                
            else:
                print(f"❌ Processing failed: {result.get('error', 'Unknown error')}")
                all_tests_passed = False
                
        except Exception as e:
            print(f"❌ Exception during processing: {str(e)}")
            all_tests_passed = False
        
        print()
    
    # Final summary
    print("=" * 80)
    if all_tests_passed:
        print("🎉 ALL ACCURACY TESTS PASSED!")
        print("   - Transaction data matches PDF exactly")
        print("   - All columns mapped correctly")
        print("   - No data misalignment detected")
        print("   - 100% accuracy achieved like repotic.in")
    else:
        print("❌ SOME ACCURACY TESTS FAILED!")
        print("   - Check if transaction data matches the original PDF")
        print("   - Verify column mapping is correct")
        print("   - Ensure no values are in wrong columns")
    print("=" * 80)
    
    return all_tests_passed

if __name__ == "__main__":
    test_accuracy_improvements()
