#!/usr/bin/env python3
"""
Test script to validate HDFC Bank extraction completeness
Ensures all transactions and fields are captured from PDF
"""

import sys
import os
import logging
import pandas as pd
from pdf_processor import PDFProcessor
from hdfc_processor import HDFCBankProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_transaction_completeness(pdf_path: str):
    """
    Test that all transactions are extracted from HDFC PDF
    """
    if not os.path.exists(pdf_path):
        logger.error(f"PDF file not found: {pdf_path}")
        return False
    
    try:
        logger.info(f"Testing transaction completeness for: {pdf_path}")
        
        # Initialize processor
        processor = PDFProcessor()
        
        # Process the PDF
        result = processor.process_pdf(pdf_path)
        
        if not result.get('success', True):
            logger.error(f"Processing failed: {result.get('message', 'Unknown error')}")
            return False
        
        transactions = result.get('transactions', [])
        bank_name = result.get('bank_name', 'Unknown')
        extraction_method = result.get('extraction_method', 'Unknown')
        
        logger.info(f"Extraction Results:")
        logger.info(f"  Bank: {bank_name}")
        logger.info(f"  Method: {extraction_method}")
        logger.info(f"  Total Transactions: {len(transactions)}")
        
        if not transactions:
            logger.warning("❌ No transactions extracted!")
            return False
        
        # Analyze field completeness
        analyze_field_completeness(transactions)
        
        # Check for missing fields
        check_missing_fields(transactions)
        
        # Display sample transactions
        display_detailed_transactions(transactions[:5])
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing completeness: {str(e)}")
        return False

def analyze_field_completeness(transactions):
    """
    Analyze completeness of all HDFC fields
    """
    logger.info("\n=== FIELD COMPLETENESS ANALYSIS ===")
    
    total = len(transactions)
    
    # Count non-empty fields
    fields = {
        'Date': sum(1 for t in transactions if t.get('date')),
        'Narration': sum(1 for t in transactions if t.get('narration', '').strip()),
        'Chq./Ref.No.': sum(1 for t in transactions if t.get('chq_ref_no', '').strip()),
        'Value Dt': sum(1 for t in transactions if t.get('value_date', '').strip()),
        'Withdrawal Amt.': sum(1 for t in transactions if t.get('withdrawal_amt') is not None),
        'Deposit Amt.': sum(1 for t in transactions if t.get('deposit_amt') is not None),
        'Closing Balance': sum(1 for t in transactions if t.get('closing_balance') is not None)
    }
    
    logger.info(f"Field Completeness (out of {total} transactions):")
    for field, count in fields.items():
        percentage = (count / total * 100) if total > 0 else 0
        status = "✅" if percentage > 90 else "⚠️" if percentage > 50 else "❌"
        logger.info(f"  {status} {field}: {count}/{total} ({percentage:.1f}%)")
    
    return fields

def check_missing_fields(transactions):
    """
    Check for transactions with missing critical fields
    """
    logger.info("\n=== MISSING FIELDS ANALYSIS ===")
    
    missing_chq_ref = [t for t in transactions if not t.get('chq_ref_no', '').strip()]
    missing_value_date = [t for t in transactions if not t.get('value_date', '').strip()]
    missing_narration = [t for t in transactions if not t.get('narration', '').strip()]
    missing_amounts = [t for t in transactions if not t.get('withdrawal_amt') and not t.get('deposit_amt')]
    
    if missing_chq_ref:
        logger.warning(f"⚠️  {len(missing_chq_ref)} transactions missing Chq./Ref.No.")
        # Show sample
        for i, txn in enumerate(missing_chq_ref[:3]):
            logger.warning(f"    Sample {i+1}: {txn.get('date')} - {txn.get('narration', '')[:50]}...")
    
    if missing_value_date:
        logger.warning(f"⚠️  {len(missing_value_date)} transactions missing Value Dt")
        # Show sample
        for i, txn in enumerate(missing_value_date[:3]):
            logger.warning(f"    Sample {i+1}: {txn.get('date')} - {txn.get('narration', '')[:50]}...")
    
    if missing_narration:
        logger.error(f"❌ {len(missing_narration)} transactions missing Narration")
    
    if missing_amounts:
        logger.error(f"❌ {len(missing_amounts)} transactions missing both amounts")

def display_detailed_transactions(transactions):
    """
    Display detailed transaction information
    """
    logger.info(f"\n=== DETAILED TRANSACTION SAMPLE ===")
    
    for i, txn in enumerate(transactions):
        logger.info(f"\nTransaction {i+1}:")
        logger.info(f"  Date: '{txn.get('date', 'MISSING')}'")
        logger.info(f"  Narration: '{txn.get('narration', 'MISSING')}'")
        logger.info(f"  Chq./Ref.No.: '{txn.get('chq_ref_no', 'MISSING')}'")
        logger.info(f"  Value Dt: '{txn.get('value_date', 'MISSING')}'")
        logger.info(f"  Withdrawal: {txn.get('withdrawal_amt', 'MISSING')}")
        logger.info(f"  Deposit: {txn.get('deposit_amt', 'MISSING')}")
        logger.info(f"  Balance: {txn.get('closing_balance', 'MISSING')}")

def compare_with_expected_count():
    """
    Helper to manually verify transaction count
    """
    logger.info("\n=== MANUAL VERIFICATION GUIDE ===")
    logger.info("To verify completeness:")
    logger.info("1. Open your HDFC PDF statement")
    logger.info("2. Count total transactions manually")
    logger.info("3. Compare with extracted count above")
    logger.info("4. Check that all pages are covered")
    logger.info("5. Verify critical fields are not showing as '-' in preview")

def test_all_extraction_strategies(pdf_path: str):
    """
    Test individual extraction strategies to see which captures most data
    """
    logger.info("\n=== TESTING EXTRACTION STRATEGIES ===")
    
    import tabula
    
    strategies = [
        {
            'name': 'Default',
            'params': {'pages': 'all', 'multiple_tables': True, 'pandas_options': {'header': None, 'dtype': str}}
        },
        {
            'name': 'Lattice',
            'params': {'pages': 'all', 'multiple_tables': True, 'lattice': True, 'pandas_options': {'header': None, 'dtype': str}}
        },
        {
            'name': 'Stream',
            'params': {'pages': 'all', 'multiple_tables': True, 'stream': True, 'pandas_options': {'header': None, 'dtype': str}}
        }
    ]
    
    hdfc_processor = HDFCBankProcessor()
    
    for strategy in strategies:
        try:
            logger.info(f"\nTesting {strategy['name']} strategy:")
            tables = tabula.read_pdf(pdf_path, **strategy['params'])
            logger.info(f"  Extracted {len(tables)} tables")
            
            total_transactions = 0
            for i, table in enumerate(tables):
                if isinstance(table, pd.DataFrame) and not table.empty:
                    transactions = hdfc_processor.process_hdfc_dataframe(table)
                    total_transactions += len(transactions)
                    logger.info(f"    Table {i+1}: {table.shape} -> {len(transactions)} transactions")
            
            logger.info(f"  Total transactions: {total_transactions}")
            
        except Exception as e:
            logger.error(f"  {strategy['name']} failed: {str(e)}")

def main():
    """
    Main test function
    """
    if len(sys.argv) != 2:
        print("Usage: python test_completeness.py <path_to_hdfc_pdf>")
        print("Example: python test_completeness.py sample_hdfc_statement.pdf")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    logger.info("🔍 HDFC Bank Extraction Completeness Test")
    logger.info("=" * 60)
    
    # Test main extraction
    success = test_transaction_completeness(pdf_path)
    
    # Test individual strategies
    test_all_extraction_strategies(pdf_path)
    
    # Manual verification guide
    compare_with_expected_count()
    
    if success:
        logger.info("\n✅ Completeness test completed!")
        logger.info("Please verify the results match your PDF exactly.")
        logger.info("If transactions are still missing, check the debug output above.")
    else:
        logger.error("\n❌ Completeness test failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
