#!/usr/bin/env python3
"""
Simple diagnostic script to identify missing transactions issue
"""

import sys
import os
import logging
import pandas as pd
import tabula
from pdf_processor import PDFProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def simple_debug(pdf_path: str):
    """
    Simple debug to identify the missing transactions issue
    """
    if not os.path.exists(pdf_path):
        logger.error(f"PDF file not found: {pdf_path}")
        return False
    
    try:
        logger.info(f"🔍 DEBUGGING MISSING TRANSACTIONS")
        logger.info(f"PDF: {pdf_path}")
        logger.info("=" * 60)
        
        # Step 1: Raw Tabula extraction
        logger.info("Step 1: Raw Tabula extraction with pages='all'")
        try:
            raw_tables = tabula.read_pdf(
                pdf_path,
                pages='all',
                multiple_tables=True,
                lattice=True,
                pandas_options={'header': None, 'dtype': str},
                silent=True
            )
            logger.info(f"Raw extraction: Found {len(raw_tables)} tables")
            
            total_raw_rows = 0
            for i, table in enumerate(raw_tables):
                if isinstance(table, pd.DataFrame) and not table.empty:
                    rows = len(table)
                    total_raw_rows += rows
                    logger.info(f"  Table {i+1}: {table.shape} ({rows} rows)")
                    
            logger.info(f"Total raw rows extracted: {total_raw_rows}")
            
        except Exception as e:
            logger.error(f"Raw extraction failed: {str(e)}")
            return False
        
        # Step 2: Full pipeline processing
        logger.info("\nStep 2: Full pipeline processing")
        try:
            processor = PDFProcessor()
            result = processor.process_pdf(pdf_path)
            
            final_transactions = result.get('transactions', [])
            logger.info(f"Final transactions: {len(final_transactions)}")
            
            if len(final_transactions) > 0:
                logger.info("Sample transactions:")
                for i, txn in enumerate(final_transactions[:3]):
                    logger.info(f"  {i+1}: {txn.get('date')} - {txn.get('narration', '')[:50]}")
                    
                logger.info("Last few transactions:")
                for i, txn in enumerate(final_transactions[-3:]):
                    logger.info(f"  {len(final_transactions)-2+i}: {txn.get('date')} - {txn.get('narration', '')[:50]}")
            
        except Exception as e:
            logger.error(f"Full pipeline failed: {str(e)}")
            return False
        
        # Step 3: Analysis
        logger.info("\nStep 3: Analysis")
        logger.info(f"Raw rows extracted: {total_raw_rows}")
        logger.info(f"Final transactions: {len(final_transactions)}")
        
        if total_raw_rows > len(final_transactions):
            loss_ratio = (total_raw_rows - len(final_transactions)) / total_raw_rows * 100
            logger.warning(f"Transaction loss: {loss_ratio:.1f}% ({total_raw_rows - len(final_transactions)} transactions lost)")
            
            if loss_ratio > 50:
                logger.error("CRITICAL: More than 50% of transactions are being lost!")
                logger.error("This indicates a serious issue in the processing pipeline")
                
                # Suggest likely causes
                logger.error("Likely causes:")
                logger.error("1. Aggressive deduplication removing legitimate transactions")
                logger.error("2. Table processing failing for some pages")
                logger.error("3. HDFC processor rejecting valid transactions")
                logger.error("4. Column mapping issues")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in simple debug: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python simple_debug.py <pdf_path>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    success = simple_debug(pdf_path)
    sys.exit(0 if success else 1)
